@import url("https://fonts.googleapis.com/css2?family=Merriweather:opsz,wght@18..144,500&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Orbitron:wght@400..900&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0); /* white */
  --foreground: oklch(0.145 0 0); /* black */
  --card: oklch(1 0 0); /* white */
  --card-foreground: oklch(0.145 0 0); /* black */
  --popover: oklch(1 0 0); /* white */
  --popover-foreground: oklch(0.145 0 0); /* black */

  /* Primary: emerald-700 #047857 - Use strategically for buttons, links, and important UI elements */
  --primary: oklch(0.51 0.158 160);
  --primary-foreground: oklch(1 0 0); /* white */

  /* Secondary: slate-700 #334155 - Neutral dark color for secondary elements */
  --secondary: oklch(0.4 0.02 240);
  --secondary-foreground: oklch(0.95 0 0); /* light gray */

  /* Muted: slate-100 #f1f5f9 - Light neutral background */
  --muted: oklch(0.97 0.01 240);
  --muted-foreground: oklch(0.5 0.02 240); /* slate-500 */

  /* Accent: slate-200 #e2e8f0 - Subtle accent for hover states */
  --accent: oklch(0.93 0.01 240);
  --accent-foreground: oklch(0.3 0.02 240); /* slate-800 */

  --destructive: oklch(0.577 0.245 27.325); /* red */

  /* Border/Focus ring: emerald-300 #6ee7b7 - Keep emerald for focus states */
  --border: oklch(0.9 0.01 240); /* slate-200 */
  --input: oklch(0.93 0.01 240); /* slate-200 */
  --ring: oklch(0.85 0.12 160); /* emerald-300 */

  /* Chart colors - keep diverse */
  --chart-1: oklch(0.51 0.158 160); /* emerald-700 */
  --chart-2: oklch(0.6 0.118 184.704); /* blue */
  --chart-3: oklch(0.398 0.07 227.392); /* purple */
  --chart-4: oklch(0.828 0.189 84.429); /* orange */
  --chart-5: oklch(0.769 0.188 70.08); /* yellow */

  /* Sidebar - use emerald as an accent, but keep it mostly neutral */
  --sidebar: oklch(0.985 0 0); /* white */
  --sidebar-foreground: oklch(0.145 0 0); /* black */
  --sidebar-primary: oklch(0.51 0.158 160); /* emerald-700 */
  --sidebar-primary-foreground: oklch(1 0 0); /* white */
  --sidebar-accent: oklch(0.93 0.01 240); /* slate-200 */
  --sidebar-accent-foreground: oklch(0.3 0.02 240); /* slate-800 */
  --sidebar-border: oklch(0.9 0.01 240); /* slate-200 */
  --sidebar-ring: oklch(0.85 0.12 160); /* emerald-300 */
}

.dark {
  --background: oklch(0.145 0 0); /* dark gray/black */
  --foreground: oklch(0.985 0 0); /* white */
  --card: oklch(0.18 0 0); /* slightly lighter than background */
  --card-foreground: oklch(0.985 0 0); /* white */
  --popover: oklch(0.18 0 0); /* slightly lighter than background */
  --popover-foreground: oklch(0.985 0 0); /* white */

  /* Primary: emerald-500 #10b981 in dark mode - brighter for dark backgrounds */
  --primary: oklch(0.72 0.17 160);
  --primary-foreground: oklch(0.145 0 0); /* black */

  /* Secondary: slate-600 #475569 - Neutral color for secondary elements */
  --secondary: oklch(0.45 0.02 240);
  --secondary-foreground: oklch(0.95 0 0); /* light gray */

  /* Muted: slate-800 #1e293b - Dark neutral background */
  --muted: oklch(0.25 0.02 240);
  --muted-foreground: oklch(0.7 0.02 240); /* slate-400 */

  /* Accent: slate-700 #334155 - Subtle accent for hover states */
  --accent: oklch(0.3 0.02 240);
  --accent-foreground: oklch(0.9 0.01 240); /* slate-200 */

  --destructive: oklch(0.704 0.191 22.216); /* red */

  /* Border/Focus ring: emerald-400 #34d399 with reduced opacity - Keep emerald for focus states */
  --border: oklch(0.3 0.02 240 / 30%); /* slate-700 with opacity */
  --input: oklch(0.25 0.02 240 / 30%); /* slate-800 with opacity */
  --ring: oklch(0.72 0.17 160 / 30%); /* emerald-500 with opacity */

  /* Chart colors - keep diverse */
  --chart-1: oklch(0.72 0.17 160); /* emerald-500 */
  --chart-2: oklch(0.696 0.17 162.48); /* blue */
  --chart-3: oklch(0.769 0.188 70.08); /* yellow */
  --chart-4: oklch(0.627 0.265 303.9); /* purple */
  --chart-5: oklch(0.645 0.246 16.439); /* red */

  /* Sidebar - use emerald as an accent, but keep it mostly neutral */
  --sidebar: oklch(0.18 0 0); /* slightly lighter than background */
  --sidebar-foreground: oklch(0.985 0 0); /* white */
  --sidebar-primary: oklch(0.72 0.17 160); /* emerald-500 */
  --sidebar-primary-foreground: oklch(0.145 0 0); /* black */
  --sidebar-accent: oklch(0.3 0.02 240); /* slate-700 */
  --sidebar-accent-foreground: oklch(0.9 0.01 240); /* slate-200 */
  --sidebar-border: oklch(0.3 0.02 240 / 30%); /* slate-700 with opacity */
  --sidebar-ring: oklch(0.72 0.17 160 / 30%); /* emerald-500 with opacity */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Merriweather", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
    font-variation-settings: "wdth" 100;
  }
}

/* Custom utility classes */
@layer utilities {
  /* Font classes */

  .font-secondary {
    font-family: "Orbitron", sans-serif;
    font-weight: 600;
    color: theme("colors.emerald.700");
  }

  /* Logo styling classes */

  .logo-large {
    height: 4.5rem;
    width: auto;
  }

  .logo-medium {
    height: 3.5rem;
    width: auto;
  }

  .logo-container {
    display: flex;
    align-items: center;
  }

  /* Custom scrollbar styles for Kanban board */
  .kanban-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .kanban-scroll::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .kanban-scroll::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .kanban-scroll::-webkit-scrollbar-thumb {
    background: rgb(203 213 225);
    border-radius: 4px;
    border: 1px solid transparent;
  }

  .kanban-scroll::-webkit-scrollbar-thumb:hover {
    background: rgb(148 163 184);
  }

  .kanban-scroll::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Kanban column auto-height support */
  .kanban-column {
    min-height: fit-content;
    height: auto;
  }

  /* Smooth scrolling for the entire page when columns get tall */
  .kanban-container {
    scroll-behavior: smooth;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Responsive styles for TaskManagement and related components */

/* Kanban Board Responsive Styles */
.kanban-container {
  /* Ensure horizontal scrolling on mobile */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Kanban Column Responsive Styles */
.kanban-column {
  /* Ensure proper touch handling */
  touch-action: manipulation;
}

/* Mobile-specific Kanban improvements */
@media (max-width: 640px) {
  .kanban-container {
    /* Remove horizontal scrolling on mobile - use grid instead */
    overflow-x: visible;
    padding-bottom: 1rem;
  }

  .kanban-column {
    /* Full width columns on mobile */
    width: 100% !important;
    min-width: unset !important;
  }
}

/* Tablet-specific Kanban improvements */
@media (min-width: 641px) and (max-width: 1023px) {
  .kanban-container {
    /* Remove horizontal scrolling on tablet - use grid instead */
    overflow-x: visible;
  }

  .kanban-column {
    /* Half width columns on tablet */
    width: 100% !important;
    min-width: unset !important;
  }
}

/* Desktop Kanban improvements */
@media (min-width: 1024px) {
  .kanban-container {
    /* Enable horizontal scrolling on desktop if needed */
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .kanban-column {
    /* Fixed width columns on desktop */
    width: 320px !important;
    min-width: 300px !important;
    flex-shrink: 0;
  }
}

/* Responsive Grid Layout for Kanban */
.kanban-grid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .kanban-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .kanban-grid {
    display: flex;
    gap: 1rem;
  }
}

/* Improve drag feedback on all devices */
.cursor-grab {
  cursor: grab;
  -webkit-user-select: none;
  user-select: none;
}

.cursor-grab:active,
.active\:cursor-grabbing:active {
  cursor: grabbing;
}

/* Better touch targets for drag handles */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Improve drag overlay on mobile */
[data-dnd-overlay] {
  z-index: 9999;
  pointer-events: none;
}

/* Milestones Page Responsive Styles */
.milestones-container {
  /* Ensure proper spacing on all devices */
  padding: 1rem;
}

@media (min-width: 640px) {
  .milestones-container {
    padding: 1.5rem;
  }
}

/* Milestone Cards Responsive */
.milestone-card {
  transition: all 0.2s ease-in-out;
}

.milestone-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Task Cards Responsive */
@media (max-width: 640px) {
  .task-card {
    /* Stack task elements vertically on mobile */
    flex-direction: column;
    align-items: stretch;
  }

  .task-controls {
    /* Full width controls on mobile */
    width: 100%;
    justify-content: space-between;
    margin-top: 0.75rem;
  }

  .task-select {
    /* Smaller select on mobile */
    min-width: 100px;
  }
}

/* Accordion Responsive */
@media (max-width: 640px) {
  .accordion-trigger {
    /* Stack accordion content on mobile */
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .accordion-content {
    /* Better spacing on mobile */
    padding: 0.75rem 0;
  }
}

/* Progress Bar Responsive */
.progress-bar-mobile {
  min-width: 4rem;
}

@media (min-width: 640px) {
  .progress-bar-mobile {
    min-width: 6rem;
  }
}

/* Dialog Responsive Improvements */
@media (max-width: 640px) {
  .dialog-content {
    /* Full screen on mobile */
    width: 95vw;
    max-height: 90vh;
    margin: 1rem;
  }

  .dialog-form {
    /* Stack form elements on mobile */
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Status Badge Responsive */
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  white-space: nowrap;
}

@media (min-width: 640px) {
  .status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

/* Mobile-specific table improvements */
@media (max-width: 768px) {
  /* Hide less important table columns on mobile */
  .table-hide-mobile {
    display: none;
  }

  /* Compact table cells */
  .table-cell-compact {
    padding: 0.5rem 0.25rem;
  }

  /* Stack table content vertically on very small screens */
  .table-stack-mobile {
    display: block;
    width: 100%;
  }

  .table-stack-mobile thead,
  .table-stack-mobile tbody,
  .table-stack-mobile th,
  .table-stack-mobile td,
  .table-stack-mobile tr {
    display: block;
  }

  .table-stack-mobile thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .table-stack-mobile tr {
    border: 1px solid #e2e8f0;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem;
    padding: 0.75rem;
    background: white;
  }
}

/* Transaction table specific responsive improvements */
@media (max-width: 1024px) {
  /* Compact transaction table on tablets and smaller */
  .transaction-table th,
  .transaction-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.875rem;
  }

  .transaction-table .user-avatar {
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  .transaction-table .user-avatar span {
    font-size: 0.75rem;
    max-width: 60px;
  }
}

@media (max-width: 640px) {
  /* Extra compact on mobile */
  .transaction-table th,
  .transaction-table td {
    padding: 0.375rem 0.125rem;
    font-size: 0.75rem;
  }
}

.table-stack-mobile td {
  border: none;
  position: relative;
  padding-left: 50% !important;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.table-stack-mobile td:before {
  content: attr(data-label) ": ";
  position: absolute;
  left: 6px;
  width: 45%;
  padding-right: 10px;
  white-space: nowrap;
  font-weight: 600;
  color: #64748b;
}

/* Improved touch targets for mobile */
@media (max-width: 640px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Larger buttons on mobile */
  .btn-mobile {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  /* Better spacing for mobile cards */
  .card-mobile {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
  }

  /* Improved modal sizing */
  .modal-mobile {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    width: calc(100vw - 2rem);
  }
}

/* Tablet-specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .kanban-column {
    min-width: 280px;
    width: 320px;
  }

  .stats-grid-tablet {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Desktop improvements */
@media (min-width: 1025px) {
  .kanban-column {
    min-width: 300px;
    width: 350px;
  }

  .stats-grid-desktop {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Utility classes for responsive design */
.hide-mobile {
  @media (max-width: 640px) {
    display: none !important;
  }
}

.hide-tablet {
  @media (min-width: 641px) and (max-width: 1024px) {
    display: none !important;
  }
}

.hide-desktop {
  @media (min-width: 1025px) {
    display: none !important;
  }
}

.show-mobile {
  display: none;
  @media (max-width: 640px) {
    display: block !important;
  }
}

.show-tablet {
  display: none;
  @media (min-width: 641px) and (max-width: 1024px) {
    display: block !important;
  }
}

.show-desktop {
  display: none;
  @media (min-width: 1025px) {
    display: block !important;
  }
}

/* Improved scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation improvements for mobile */
@media (prefers-reduced-motion: no-preference) {
  .animate-mobile {
    transition: all 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Reduce animations on mobile for better performance */
@media (max-width: 640px) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus improvements for accessibility */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Better contrast for mobile */
@media (max-width: 640px) {
  .text-contrast {
    color: #1e293b;
    font-weight: 500;
  }

  .bg-contrast {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
  }
}

/* Notification scrollbar styles */
.notification-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgb(209 213 219) transparent;
}

.notification-scroll::-webkit-scrollbar {
  width: 6px;
}

.notification-scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.notification-scroll::-webkit-scrollbar-thumb {
  background: rgb(209 213 219);
  border-radius: 3px;
}

.notification-scroll::-webkit-scrollbar-thumb:hover {
  background: rgb(156 163 175);
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
